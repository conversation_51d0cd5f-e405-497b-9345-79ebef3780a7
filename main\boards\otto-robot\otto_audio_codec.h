#ifndef _OTTO_AUDIO_CODEC_H
#define _OTTO_AUDIO_CODEC_H

#include "audio_codec.h"

#include <driver/gpio.h>
#include <driver/i2s_std.h>
#include <vector>

class OttoAudioCodec : public AudioCodec {
private:
    // ref buffer used for software AEC
    std::vector<int16_t> ref_buffer_;
    int read_pos_ = 0;
    int write_pos_ = 0;

    virtual int Read(int16_t* dest, int samples) override;
    virtual int Write(const int16_t* data, int samples) override;

public:
    OttoAudioCodec(int input_sample_rate, int output_sample_rate, 
                   gpio_num_t spk_bclk, gpio_num_t spk_ws, gpio_num_t spk_dout, 
                   gpio_num_t mic_sck, gpio_num_t mic_ws, gpio_num_t mic_din, 
                   bool input_reference);
    virtual ~OttoAudioCodec();

    virtual void SetOutputVolume(int volume) override;
    virtual void EnableInput(bool enable) override;
    virtual void EnableOutput(bool enable) override;
    virtual void Start() override;
};

#endif // _OTTO_AUDIO_CODEC_H
