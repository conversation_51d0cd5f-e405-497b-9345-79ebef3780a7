#include "otto_audio_codec.h"

#include <esp_log.h>
#include <driver/i2s_common.h>
#include <cstring>
#include "settings.h"

static const char TAG[] = "OttoAudioCodec";

OttoAudioCodec::OttoAudioCodec(int input_sample_rate, int output_sample_rate, 
                               gpio_num_t spk_bclk, gpio_num_t spk_ws, gpio_num_t spk_dout, 
                               gpio_num_t mic_sck, gpio_num_t mic_ws, gpio_num_t mic_din, 
                               bool input_reference) {
    duplex_ = false; // 使用单工模式（分离的输入输出通道）
    input_reference_ = input_reference; // 是否使用软件参考输入，实现回声消除
    input_sample_rate_ = input_sample_rate;
    output_sample_rate_ = output_sample_rate;
    input_channels_ = input_reference_ ? 2 : 1; // 输入通道数（麦克风 + 软件参考信号）
    output_channels_ = 1; // 输出通道数

    if (input_reference) {
        // 分配参考信号缓冲区，大小为约40ms的音频数据
        ref_buffer_.resize(960 * 2);
        ESP_LOGI(TAG, "Software reference buffer allocated: %d samples", ref_buffer_.size());
    }

    // Create speaker output channel
    i2s_chan_config_t chan_cfg = I2S_CHANNEL_DEFAULT_CONFIG(I2S_NUM_0, I2S_ROLE_MASTER);
    chan_cfg.auto_clear = true;
    ESP_ERROR_CHECK(i2s_new_channel(&chan_cfg, &tx_handle_, nullptr));

    i2s_std_config_t std_cfg = {
        .clk_cfg = I2S_STD_CLK_DEFAULT_CONFIG(output_sample_rate),
        .slot_cfg = I2S_STD_PHILIPS_SLOT_DEFAULT_CONFIG(I2S_DATA_BIT_WIDTH_16BIT, I2S_SLOT_MODE_MONO),
        .gpio_cfg = {
            .mclk = I2S_GPIO_UNUSED,
            .bclk = spk_bclk,
            .ws = spk_ws,
            .dout = spk_dout,
            .din = I2S_GPIO_UNUSED,
            .invert_flags = {
                .mclk_inv = false,
                .bclk_inv = false,
                .ws_inv = false,
            },
        },
    };
    ESP_ERROR_CHECK(i2s_channel_init_std_mode(tx_handle_, &std_cfg));

    // Create microphone input channel
    chan_cfg = I2S_CHANNEL_DEFAULT_CONFIG(I2S_NUM_1, I2S_ROLE_MASTER);
    chan_cfg.auto_clear = true;
    ESP_ERROR_CHECK(i2s_new_channel(&chan_cfg, nullptr, &rx_handle_));

    std_cfg = {
        .clk_cfg = I2S_STD_CLK_DEFAULT_CONFIG(input_sample_rate),
        .slot_cfg = I2S_STD_PHILIPS_SLOT_DEFAULT_CONFIG(I2S_DATA_BIT_WIDTH_16BIT, I2S_SLOT_MODE_MONO),
        .gpio_cfg = {
            .mclk = I2S_GPIO_UNUSED,
            .bclk = mic_sck,
            .ws = mic_ws,
            .dout = I2S_GPIO_UNUSED,
            .din = mic_din,
            .invert_flags = {
                .mclk_inv = false,
                .bclk_inv = false,
                .ws_inv = false,
            },
        },
    };
    ESP_ERROR_CHECK(i2s_channel_init_std_mode(rx_handle_, &std_cfg));

    ESP_LOGI(TAG, "OttoAudioCodec initialized with software reference: %s", 
             input_reference ? "enabled" : "disabled");
}

OttoAudioCodec::~OttoAudioCodec() {
    if (tx_handle_) {
        i2s_del_channel(tx_handle_);
    }
    if (rx_handle_) {
        i2s_del_channel(rx_handle_);
    }
}

void OttoAudioCodec::SetOutputVolume(int volume) {
    output_volume_ = volume;
    ESP_LOGI(TAG, "Set output volume to %d", output_volume_);
    
    Settings settings("audio", true);
    settings.SetInt("output_volume", output_volume_);
}

void OttoAudioCodec::EnableInput(bool enable) {
    if (enable == input_enabled_) {
        return;
    }
    if (enable) {
        ESP_ERROR_CHECK(i2s_channel_enable(rx_handle_));
    } else {
        ESP_ERROR_CHECK(i2s_channel_disable(rx_handle_));
    }
    AudioCodec::EnableInput(enable);
}

void OttoAudioCodec::EnableOutput(bool enable) {
    if (enable == output_enabled_) {
        return;
    }
    if (enable) {
        ESP_ERROR_CHECK(i2s_channel_enable(tx_handle_));
    } else {
        ESP_ERROR_CHECK(i2s_channel_disable(tx_handle_));
    }
    AudioCodec::EnableOutput(enable);
}

void OttoAudioCodec::Start() {
    Settings settings("audio", false);
    output_volume_ = settings.GetInt("output_volume", output_volume_);
    if (output_volume_ <= 0) {
        ESP_LOGW(TAG, "Output volume value (%d) is too small, setting to default (70)", output_volume_);
        output_volume_ = 70;
    }

    EnableInput(true);
    EnableOutput(true);
    ESP_LOGI(TAG, "Otto audio codec started");
}

int OttoAudioCodec::Read(int16_t* dest, int samples) {
    if (!input_enabled_) {
        return 0;
    }

    if (!input_reference_) {
        // 简单模式：只读取麦克风数据
        size_t bytes_read = 0;
        esp_err_t ret = i2s_channel_read(rx_handle_, dest, samples * sizeof(int16_t), &bytes_read, portMAX_DELAY);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "Failed to read from microphone: %s", esp_err_to_name(ret));
            return 0;
        }
        return bytes_read / sizeof(int16_t);
    } else {
        // 软件参考信号模式：交错排列麦克风数据和参考信号
        int mic_samples = samples / input_channels_;
        std::vector<int16_t> mic_data(mic_samples);
        
        // 读取麦克风数据
        size_t bytes_read = 0;
        esp_err_t ret = i2s_channel_read(rx_handle_, mic_data.data(), mic_samples * sizeof(int16_t), &bytes_read, portMAX_DELAY);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "Failed to read from microphone: %s", esp_err_to_name(ret));
            return 0;
        }
        
        int actual_mic_samples = bytes_read / sizeof(int16_t);
        
        // 交错排列麦克风数据和参考信号
        int j = 0;
        int i = 0;
        while (j < actual_mic_samples && i < samples) {
            // 麦克风数据
            dest[i++] = mic_data[j++];
            // 参考信号数据
            dest[i++] = (read_pos_ < write_pos_) ? ref_buffer_[read_pos_++] : 0;
        }
        
        // 重置缓冲区位置
        if (read_pos_ == write_pos_) {
            read_pos_ = write_pos_ = 0;
        }
        
        return i;
    }
}

int OttoAudioCodec::Write(const int16_t* data, int samples) {
    if (!output_enabled_) {
        return 0;
    }

    // 写入扬声器
    size_t bytes_written = 0;
    esp_err_t ret = i2s_channel_write(tx_handle_, data, samples * sizeof(int16_t), &bytes_written, portMAX_DELAY);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to write to speaker: %s", esp_err_to_name(ret));
        return 0;
    }

    // 如果启用了软件参考信号，将输出数据复制到参考缓冲区
    if (input_reference_) {
        // 检查缓冲区溢出
        if (write_pos_ - read_pos_ + samples > ref_buffer_.size()) {
            // 写溢出，只保留最近的数据
            if (ref_buffer_.size() >= samples) {
                read_pos_ = write_pos_ + samples - ref_buffer_.size();
            } else {
                // 如果单次写入的数据超过缓冲区大小，清空缓冲区
                read_pos_ = write_pos_ = 0;
                ESP_LOGW(TAG, "Reference buffer overflow, clearing buffer");
                return bytes_written / sizeof(int16_t);
            }
        }
        
        // 压缩缓冲区
        if (read_pos_ > 0) {
            if (write_pos_ != read_pos_) {
                memmove(ref_buffer_.data(), ref_buffer_.data() + read_pos_, 
                       (write_pos_ - read_pos_) * sizeof(int16_t));
            }
            write_pos_ -= read_pos_;
            read_pos_ = 0;
        }
        
        // 复制新数据到参考缓冲区
        memcpy(&ref_buffer_[write_pos_], data, samples * sizeof(int16_t));
        write_pos_ += samples;
    }

    return bytes_written / sizeof(int16_t);
}
